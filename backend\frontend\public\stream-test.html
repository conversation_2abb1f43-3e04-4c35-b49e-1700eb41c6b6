<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stream Test - Exact StreamPlayer Structure</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .stream-player-container {
            max-width: 800px;
            margin: 0 auto;
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
        }
        .video-container {
            position: relative;
            width: 100%;
            height: 400px;
            background: #000;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        .video-element {
            width: 100%;
            height: 100%;
            object-fit: cover;
            background-color: #000;
            display: block;
        }
        .streamer-controls {
            text-align: center;
            padding: 20px;
        }
        .stream-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        button {
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 6px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }
        .debug-info {
            margin-top: 1rem;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            font-size: 0.9rem;
            color: #e0e0e0;
        }
        .error {
            color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
            border: 1px solid rgba(255, 107, 107, 0.3);
        }
    </style>
</head>
<body>
    <div class="stream-player-container">
        <h1>🎥 Stream Test - Exact StreamPlayer Structure</h1>
        <p>This replicates the exact same HTML structure as the React StreamPlayer component.</p>
        
        <div class="video-container">
            <video
                id="video"
                autoplay
                muted
                playsinline
                class="video-element"
            ></video>
        </div>

        <div class="streamer-controls">
            <h3>Ready to start streaming?</h3>
            <p>Click the button below to start your live stream</p>
            
            <div class="stream-buttons">
                <button onclick="testVideoElement()">Test Video Element</button>
                <button onclick="simpleCameraTest()">Simple Camera Test</button>
                <button onclick="startStreaming()">Start Streaming</button>
            </div>
            
            <div id="debugInfo" class="debug-info">Ready to test</div>
        </div>
    </div>

    <script>
        const video = document.getElementById('video');
        const debugInfo = document.getElementById('debugInfo');
        let currentStream = null;

        function updateDebug(message, isError = false) {
            debugInfo.textContent = message;
            debugInfo.className = 'debug-info' + (isError ? ' error' : '');
            console.log(message);
        }

        function testVideoElement() {
            console.log('=== VIDEO ELEMENT TEST ===');
            console.log('video element:', video);
            console.log('Video element details:', {
                exists: !!video,
                tagName: video?.tagName,
                id: video?.id,
                className: video?.className,
                parentElement: video?.parentElement,
                style: video?.style?.cssText
            });
            updateDebug(`Video element exists: ${!!video}`);
        }

        async function simpleCameraTest() {
            console.log('=== SIMPLE CAMERA TEST START ===');
            try {
                updateDebug('Simple test: requesting camera...');
                
                const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                console.log('Simple test: got stream', stream);
                
                if (video) {
                    video.srcObject = stream;
                    video.muted = true;
                    await video.play();
                    updateDebug('Simple test: SUCCESS!');
                    console.log('Simple test: SUCCESS!');
                    currentStream = stream;
                }
            } catch (error) {
                console.error('Simple test failed:', error);
                updateDebug(`Simple test failed: ${error.message}`, true);
            }
            console.log('=== SIMPLE CAMERA TEST END ===');
        }

        async function startStreaming() {
            console.log('=== START STREAMING FUNCTION CALLED ===');
            console.log('Function context:', {
                video: video,
                videoExists: !!video
            });

            try {
                updateDebug('Starting streaming process...');
                console.log('=== ATTEMPTING CAMERA ACCESS ===');

                // Check browser support first
                if (!navigator.mediaDevices) {
                    throw new Error('navigator.mediaDevices not supported');
                }

                if (!navigator.mediaDevices.getUserMedia) {
                    throw new Error('getUserMedia not supported');
                }

                console.log('Browser supports camera API');
                updateDebug('Browser supports camera, requesting access...');

                // Request camera with detailed logging
                console.log('Calling getUserMedia...');
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: true,
                    audio: true
                });

                console.log('SUCCESS: Got camera stream!', stream);
                console.log('Stream details:', {
                    id: stream.id,
                    active: stream.active,
                    videoTracks: stream.getVideoTracks().length,
                    audioTracks: stream.getAudioTracks().length
                });

                updateDebug(`SUCCESS: Camera stream obtained! Video tracks: ${stream.getVideoTracks().length}`);

                // Check video element
                console.log('Checking video element:', {
                    video: video,
                    videoType: typeof video,
                    videoTagName: video?.tagName
                });

                if (video) {
                    console.log('Setting video source...');
                    video.srcObject = stream;
                    video.muted = true;

                    console.log('Attempting to play video...');
                    try {
                        await video.play();
                        console.log('SUCCESS: Video is playing!');
                        updateDebug('SUCCESS: Camera is working! Video playing.');
                        currentStream = stream;
                    } catch (playError) {
                        console.error('Video play failed:', playError);
                        updateDebug(`Video play failed: ${playError.message}`, true);
                    }
                } else {
                    console.error('ERROR: Video element not found!');
                    updateDebug('ERROR: Video element not found', true);
                }

                console.log('=== CAMERA SETUP COMPLETE ===');

            } catch (error) {
                console.error('=== CAMERA ACCESS FAILED ===');
                console.error('Error details:', {
                    name: error.name,
                    message: error.message,
                    stack: error.stack
                });
                updateDebug(`Camera failed: ${error.name} - ${error.message}`, true);
            }
        }

        function stopCamera() {
            if (currentStream) {
                currentStream.getTracks().forEach(track => track.stop());
                currentStream = null;
                video.srcObject = null;
                updateDebug('Camera stopped');
            }
        }

        // Auto-test video element on load
        window.onload = () => {
            testVideoElement();
        };
    </script>
</body>
</html>
