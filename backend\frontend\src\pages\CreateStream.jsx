import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { streamsAPI } from '../services/api';
import './CreateStream.css';

const CreateStream = () => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const navigate = useNavigate();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const response = await streamsAPI.createStream(formData);
      const streamId = response.data.id;
      // Navigate to the stream page so user can start streaming
      navigate(`/stream/${streamId}`);
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to create stream');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="create-stream-container">
      <div className="create-stream-card">
        <h2>Create New Stream</h2>
        
        {error && <div className="error-message">{error}</div>}
        
        <form onSubmit={handleSubmit} className="create-stream-form">
          <div className="form-group">
            <label htmlFor="title">Stream Title *</label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              required
              placeholder="Enter an engaging title for your stream"
              maxLength="255"
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Tell viewers what your stream is about..."
              rows="4"
              maxLength="1000"
            />
          </div>
          
          <div className="stream-info">
            <h3>Stream Setup Information</h3>
            <div className="info-box">
              <p><strong>Getting Started:</strong></p>
              <ol>
                <li>Create your stream using this form</li>
                <li>You'll receive a unique stream key</li>
                <li>Use streaming software like OBS to broadcast</li>
                <li>Start your stream from the dashboard</li>
              </ol>
            </div>
          </div>
          
          <div className="form-actions">
            <button 
              type="button" 
              onClick={() => navigate('/dashboard')}
              className="cancel-button"
            >
              Cancel
            </button>
            <button 
              type="submit" 
              className="create-button"
              disabled={loading}
            >
              {loading ? 'Creating Stream...' : 'Create Stream'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateStream;
