# LiveStream Platform

A full-stack live streaming application built with <PERSON><PERSON> (backend) and <PERSON><PERSON> (frontend) featuring user authentication and real-time video streaming capabilities.

## Features

- **User Authentication**: Registration, login, and logout functionality
- **Stream Management**: Create, start, stop, and delete live streams
- **Live Streaming**: Real-time video streaming using WebRTC
- **Stream Discovery**: Browse and watch live streams from other users
- **Dashboard**: Manage your streams and view analytics
- **Responsive Design**: Works on desktop and mobile devices

## Tech Stack

### Backend (Laravel)
- Laravel 12.x
- Laravel Sanctum for API authentication
- SQLite database
- RESTful API design
- CORS configuration for frontend integration

### Frontend (React)
- React 18 with Vite
- React Router for navigation
- Axios for API communication
- WebRTC for live streaming
- Simple-peer for peer connections
- Socket.io for real-time communication

## Installation

### Prerequisites
- PHP 8.2+
- Composer
- Node.js 18+
- npm

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Install PHP dependencies:
   ```bash
   composer install
   ```

3. Copy environment file:
   ```bash
   cp .env.example .env
   ```

4. Generate application key:
   ```bash
   php artisan key:generate
   ```

5. Run database migrations:
   ```bash
   php artisan migrate
   ```

6. Start the Laravel development server:
   ```bash
   php artisan serve
   ```

The backend will be available at `http://localhost:8000`

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd backend/frontend
   ```

2. Install Node.js dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

The frontend will be available at `http://localhost:5173`

### Signaling Server Setup

1. Navigate to the signaling server directory:
   ```bash
   cd signaling-server
   ```

2. Install Node.js dependencies:
   ```bash
   npm install
   ```

3. Start the signaling server:
   ```bash
   npm start
   ```

The signaling server will be available at `http://localhost:8001`

## API Endpoints

### Authentication
- `POST /api/register` - User registration
- `POST /api/login` - User login
- `POST /api/logout` - User logout (requires auth)
- `GET /api/user` - Get authenticated user (requires auth)

### Streams
- `GET /api/streams` - Get all live streams
- `GET /api/streams/{id}` - Get specific stream
- `POST /api/streams` - Create new stream (requires auth)
- `PUT /api/streams/{id}` - Update stream (requires auth)
- `DELETE /api/streams/{id}` - Delete stream (requires auth)
- `POST /api/streams/{id}/join` - Join stream as viewer (requires auth)
- `POST /api/streams/{id}/leave` - Leave stream (requires auth)
- `GET /api/my-streams` - Get user's streams (requires auth)

## Usage

### Current Demo Mode
The application currently runs in demo mode where:
- **Streamers** can create streams and see their own camera feed
- **Viewers** will see a placeholder (requires signaling server for full functionality)
- All other features (authentication, stream management, API) work fully

### Full Usage (with signaling server)
1. **Register/Login**: Create an account or login to access streaming features
2. **Create Stream**: Go to "Create Stream" to set up a new live stream
3. **Start Streaming**: Use the stream controls to start broadcasting
4. **Watch Streams**: Browse live streams on the home page and click to watch
5. **Manage Streams**: Use the dashboard to manage your streams

### Setting Up Full WebRTC Streaming

To enable full peer-to-peer streaming, you need to set up a Socket.io signaling server:

1. **Create a Node.js signaling server**:
```javascript
// server.js
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: ["http://localhost:5173", "http://localhost:3000"],
    methods: ["GET", "POST"]
  }
});

io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  socket.on('join-stream', (data) => {
    socket.join(data.streamId);
    socket.to(data.streamId).emit('viewer-joined', { viewerId: socket.id });
  });

  socket.on('signal', (data) => {
    socket.to(data.streamId).emit('signal', data);
  });

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

server.listen(8000, () => {
  console.log('Signaling server running on port 8000');
});
```

2. **Install dependencies**:
```bash
npm init -y
npm install express socket.io
node server.js
```

3. **Update the WebRTC service** to point to your signaling server URL

## Database Schema

### Users Table
- id, name, email, password, timestamps

### Streams Table
- id, user_id, title, description, stream_key, status, thumbnail_url, viewer_count, started_at, ended_at, timestamps

### Stream Viewers Table
- id, stream_id, user_id, joined_at, left_at, timestamps

## Development Notes

- The WebRTC implementation is basic and suitable for development/testing
- For production, consider using a media server like Kurento or Janus
- Socket.io server implementation is not included but referenced in the WebRTC service
- CORS is configured to allow requests from localhost:3000 and localhost:5173

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.
