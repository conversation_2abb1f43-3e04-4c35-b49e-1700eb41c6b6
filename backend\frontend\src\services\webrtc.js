// Native WebRTC implementation without simple-peer

class WebRTCService {
  constructor() {
    this.socket = null;
    this.peerConnection = null;
    this.peerConnections = new Map(); // For streamers to handle multiple viewers
    this.localStream = null;
    this.isStreamer = false;
    this.streamId = null;
    this.onStreamCallback = null;
    this.onViewerJoinCallback = null;
    this.onViewerLeaveCallback = null;

    // WebRTC configuration
    this.rtcConfig = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:global.stun.twilio.com:3478' }
      ]
    };
  }

  // Initialize socket connection
  async initSocket() {
    try {
      // Dynamically import socket.io-client
      const { io } = await import('socket.io-client');
      this.socket = io('http://localhost:8001', {
        transports: ['websocket', 'polling'],
        timeout: 3000,
        forceNew: true
      });

      // Set up connection timeout
      const connectionTimeout = setTimeout(() => {
        if (this.socket && !this.socket.connected) {
          console.warn('Socket.io connection timeout - no signaling server available');
          this.socket.disconnect();
          this.socket = null;
        }
      }, 3000);

      this.socket.on('connect', () => {
        clearTimeout(connectionTimeout);
        console.log('Connected to signaling server');
      });

      this.socket.on('connect_error', (error) => {
        clearTimeout(connectionTimeout);
        console.warn('Socket.io connection failed - using local streaming only:', error.message);
        this.socket = null;
        return null;
      });

      this.setupSocketEvents();
      return this.socket;
    } catch (error) {
      console.warn('Socket.io not available, using local streaming only:', error);
      return null;
    }
  }

  setupSocketEvents() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Connected to signaling server');
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from signaling server');
    });

    this.socket.on('connect_error', (error) => {
      console.warn('Socket connection error:', error);
    });

    this.socket.on('viewer-joined', (data) => {
      console.log('Viewer joined:', data);
      if (this.isStreamer && this.onViewerJoinCallback) {
        this.onViewerJoinCallback(data);
      }
    });

    this.socket.on('viewer-left', (data) => {
      console.log('Viewer left:', data);
      if (this.isStreamer && this.onViewerLeaveCallback) {
        this.onViewerLeaveCallback(data);
      }
    });

    this.socket.on('viewer-wants-stream', (data) => {
      console.log('Viewer wants stream:', data);
      console.log('Current state:', {
        isStreamer: this.isStreamer,
        hasLocalStream: !!this.localStream,
        localStreamTracks: this.localStream?.getTracks().length
      });

      if (this.isStreamer && this.localStream) {
        console.log('Creating peer connection for viewer:', data.viewerId);
        this.createPeerForViewer(data.viewerId);
      } else {
        console.warn('Cannot create peer - not streamer or no local stream');
      }
    });

    this.socket.on('signal', async (data) => {
      console.log('Received signal:', data);
      console.log('Signal type:', data.signal?.type);
      console.log('From:', data.from);

      try {
        if (data.signal.type === 'offer' && this.peerConnection) {
          console.log('Viewer: Processing offer from streamer');
          console.log('Peer connection state before offer:', this.peerConnection.signalingState);

          if (this.peerConnection.signalingState === 'stable') {
            await this.peerConnection.setRemoteDescription(data.signal.offer);
            const answer = await this.peerConnection.createAnswer({
              offerToReceiveAudio: true,
              offerToReceiveVideo: true
            });
            await this.peerConnection.setLocalDescription(answer);

            console.log('Created answer with SDP:', answer.sdp.substring(0, 200) + '...');

            console.log('Viewer: Sending answer to streamer');
            this.socket.emit('signal', {
              streamId: this.streamId,
              signal: {
                type: 'answer',
                answer: answer
              },
              to: data.from
            });
          } else {
            console.warn('Viewer: Cannot process offer, peer connection not in stable state:', this.peerConnection.signalingState);
          }
        } else if (data.signal.type === 'answer' && this.peerConnections.has(data.from)) {
          console.log('Streamer: Processing answer from viewer');
          const peerConnection = this.peerConnections.get(data.from);
          await peerConnection.setRemoteDescription(data.signal.answer);
          console.log('Streamer: Answer processed successfully');
        } else if (data.signal.type === 'ice-candidate') {
          console.log('Processing ICE candidate');
          const candidate = new RTCIceCandidate(data.signal.candidate);
          if (this.peerConnection) {
            await this.peerConnection.addIceCandidate(candidate);
            console.log('Viewer: ICE candidate added');
          } else if (this.peerConnections.has(data.from)) {
            const peerConnection = this.peerConnections.get(data.from);
            await peerConnection.addIceCandidate(candidate);
            console.log('Streamer: ICE candidate added for viewer', data.from);
          }
        } else {
          console.warn('Unhandled signal or missing peer connection:', {
            signalType: data.signal?.type,
            hasPeerConnection: !!this.peerConnection,
            hasPeerConnections: this.peerConnections.size,
            from: data.from
          });
        }
      } catch (error) {
        console.error('Error handling signal:', error);
      }
    });

    this.socket.on('stream-ended', (data) => {
      console.log('Stream ended:', data);
      if (this.onStreamCallback) {
        this.onStreamCallback(null); // Signal that stream ended
      }
    });
  }

  // Start streaming (for streamers)
  async startStreaming(streamId, constraints = { video: true, audio: true }) {
    try {
      this.streamId = streamId;
      this.isStreamer = true;

      console.log('Requesting camera access...');

      // Check if getUserMedia is available
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera access not supported in this browser');
      }

      // Get user media with proper constraints
      this.localStream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          frameRate: { ideal: 30 }
        },
        audio: true
      });

      console.log('Camera access granted, stream obtained:', this.localStream);

      if (!this.socket) {
        const socket = await this.initSocket();
        if (!socket) {
          console.warn('Socket.io not available, using local streaming only');
        }
      }

      // Start the stream on signaling server
      if (this.socket) {
        this.socket.emit('start-stream', { streamId });
        console.log('Stream started on signaling server');
      }

      return this.localStream;
    } catch (error) {
      console.error('Error starting stream:', error);

      // Provide more specific error messages
      if (error.name === 'NotAllowedError') {
        throw new Error('Camera access denied. Please allow camera permissions and try again.');
      } else if (error.name === 'NotFoundError') {
        throw new Error('No camera found. Please connect a camera and try again.');
      } else if (error.name === 'NotReadableError') {
        throw new Error('Camera is already in use by another application.');
      } else {
        throw new Error(`Failed to access camera: ${error.message}`);
      }
    }
  }

  // Watch stream as viewer
  async watchStream(streamId) {
    try {
      this.streamId = streamId;
      this.isStreamer = false;

      if (!this.socket) {
        const socket = await this.initSocket();
        if (!socket) {
          console.warn('Socket.io not available, using demo mode');
          // For demo purposes, show a message
          setTimeout(() => {
            if (this.onStreamCallback) {
              console.log('Demo mode: No real stream available');
              this.onStreamCallback(null);
            }
          }, 1000);
          return true;
        }
      }

      // Start watching stream
      if (this.socket) {
        this.socket.emit('watch-stream', { streamId });
      }

      // Create WebRTC peer connection
      this.peerConnection = new RTCPeerConnection(this.rtcConfig);

      // Handle incoming stream
      this.peerConnection.ontrack = (event) => {
        console.log('Received track from streamer:', event.track);
        console.log('Track details:', {
          kind: event.track.kind,
          enabled: event.track.enabled,
          readyState: event.track.readyState,
          settings: event.track.getSettings()
        });

        if (event.streams && event.streams[0]) {
          const stream = event.streams[0];
          console.log('Received stream from streamer:', stream);
          console.log('Stream details:', {
            id: stream.id,
            active: stream.active,
            videoTracks: stream.getVideoTracks().length,
            audioTracks: stream.getAudioTracks().length
          });

          // Log video track details
          stream.getVideoTracks().forEach((track, index) => {
            console.log(`Received video track ${index}:`, {
              kind: track.kind,
              enabled: track.enabled,
              readyState: track.readyState,
              settings: track.getSettings()
            });
          });

          if (this.onStreamCallback) {
            this.onStreamCallback(stream);
          }
        }
      };

      // Handle ICE candidates
      this.peerConnection.onicecandidate = (event) => {
        if (event.candidate && this.socket) {
          this.socket.emit('signal', {
            streamId,
            signal: {
              type: 'ice-candidate',
              candidate: event.candidate
            },
            to: 'streamer'
          });
        }
      };

      // Handle connection state changes
      this.peerConnection.onconnectionstatechange = () => {
        console.log('Connection state:', this.peerConnection.connectionState);
        console.log('Signaling state:', this.peerConnection.signalingState);
        console.log('ICE connection state:', this.peerConnection.iceConnectionState);

        if (this.peerConnection.connectionState === 'failed') {
          console.error('WebRTC connection failed');
          if (this.onStreamCallback) {
            this.onStreamCallback(null);
          }
        }
      };

      // Add signaling state change monitoring
      this.peerConnection.onsignalingstatechange = () => {
        console.log('Signaling state changed to:', this.peerConnection.signalingState);
      };

      return true;
    } catch (error) {
      console.error('Error watching stream:', error);
      throw error;
    }
  }

  // Create peer connection for new viewer (streamer side)
  async createPeerForViewer(viewerId) {
    if (!this.isStreamer || !this.localStream) {
      return null;
    }

    console.log('Creating peer connection for viewer:', viewerId);
    console.log('Local stream details:', {
      id: this.localStream.id,
      active: this.localStream.active,
      videoTracks: this.localStream.getVideoTracks().length,
      audioTracks: this.localStream.getAudioTracks().length
    });

    // Log video track details
    this.localStream.getVideoTracks().forEach((track, index) => {
      console.log(`Video track ${index}:`, {
        kind: track.kind,
        enabled: track.enabled,
        readyState: track.readyState,
        settings: track.getSettings(),
        constraints: track.getConstraints()
      });
    });

    try {
      const peerConnection = new RTCPeerConnection(this.rtcConfig);

      // Add local stream to peer connection with detailed logging
      console.log('About to add tracks to peer connection...');
      this.localStream.getTracks().forEach((track, index) => {
        console.log(`Adding track ${index} to peer connection:`, {
          kind: track.kind,
          enabled: track.enabled,
          readyState: track.readyState,
          settings: track.getSettings()
        });
        const sender = peerConnection.addTrack(track, this.localStream);
        console.log('Track added, sender:', sender);
      });

      console.log('All tracks added to peer connection');

      // Handle ICE candidates
      peerConnection.onicecandidate = (event) => {
        if (event.candidate && this.socket) {
          this.socket.emit('signal', {
            streamId: this.streamId,
            signal: {
              type: 'ice-candidate',
              candidate: event.candidate
            },
            to: viewerId
          });
        }
      };

      // Create offer for viewer with video constraints
      const offer = await peerConnection.createOffer({
        offerToReceiveAudio: true,
        offerToReceiveVideo: true
      });
      await peerConnection.setLocalDescription(offer);

      console.log('Created offer with SDP:', offer.sdp.substring(0, 200) + '...');

      // Send offer to viewer
      if (this.socket) {
        this.socket.emit('signal', {
          streamId: this.streamId,
          signal: {
            type: 'offer',
            offer: offer
          },
          to: viewerId
        });
      }

      // Store the peer connection
      this.peerConnections.set(viewerId, peerConnection);

      console.log('Offer sent to viewer:', viewerId);
      return peerConnection;
    } catch (error) {
      console.error('Error creating peer connection for viewer:', error);
      return null;
    }
  }

  // Stop streaming
  stopStreaming() {
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    // Close all peer connections
    this.peerConnections.forEach((peerConnection, viewerId) => {
      peerConnection.close();
    });
    this.peerConnections.clear();

    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    if (this.socket) {
      this.socket.emit('stop-stream', { streamId: this.streamId });
    }

    this.isStreamer = false;
    this.streamId = null;
  }

  // Stop watching stream
  stopWatching() {
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    if (this.socket) {
      this.socket.emit('stop-watching', { streamId: this.streamId });
    }

    this.isStreamer = false;
    this.streamId = null;
  }

  // Set callbacks
  onStream(callback) {
    this.onStreamCallback = callback;
  }

  onViewerJoin(callback) {
    this.onViewerJoinCallback = callback;
  }

  onViewerLeave(callback) {
    this.onViewerLeaveCallback = callback;
  }

  // Check if WebRTC is supported
  isSupported() {
    return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
  }
}

export default new WebRTCService();
