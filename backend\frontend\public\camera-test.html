<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Camera Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        video {
            width: 100%;
            max-width: 640px;
            height: 480px;
            background-color: #000;
            border: 2px solid #333;
            border-radius: 8px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
            font-weight: bold;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 Simple Camera Test</h1>
        <p>This is a basic HTML page to test camera access without React/Vite.</p>
        
        <div>
            <button onclick="checkSupport()">Check Support</button>
            <button onclick="listDevices()">List Devices</button>
            <button onclick="startCamera()">Start Camera</button>
            <button onclick="stopCamera()">Stop Camera</button>
        </div>
        
        <div id="status" class="status">Ready to test camera</div>
        
        <video id="video" autoplay muted playsinline></video>
        
        <div id="info"></div>
    </div>

    <script>
        const video = document.getElementById('video');
        const status = document.getElementById('status');
        const info = document.getElementById('info');
        let currentStream = null;

        function updateStatus(message, type = 'info') {
            status.textContent = message;
            status.className = 'status';
            if (type === 'error') status.classList.add('error');
            if (type === 'success') status.classList.add('success');
            console.log(message);
        }

        function checkSupport() {
            updateStatus('Checking browser support...');
            
            const results = [];
            results.push(`URL: ${window.location.href}`);
            results.push(`Protocol: ${window.location.protocol}`);
            results.push(`User Agent: ${navigator.userAgent}`);
            results.push(`MediaDevices supported: ${!!navigator.mediaDevices}`);
            results.push(`getUserMedia supported: ${!!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)}`);
            results.push(`Secure context: ${window.isSecureContext}`);
            
            info.innerHTML = '<h3>Browser Info:</h3><ul>' + 
                results.map(r => `<li>${r}</li>`).join('') + '</ul>';
            
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                updateStatus('✅ Camera API is supported!', 'success');
            } else {
                updateStatus('❌ Camera API not supported', 'error');
            }
        }

        async function listDevices() {
            try {
                updateStatus('Listing available devices...');
                
                if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
                    throw new Error('Device enumeration not supported');
                }

                const devices = await navigator.mediaDevices.enumerateDevices();
                const videoDevices = devices.filter(d => d.kind === 'videoinput');
                const audioDevices = devices.filter(d => d.kind === 'audioinput');
                
                const deviceInfo = [
                    `Total devices: ${devices.length}`,
                    `Video devices: ${videoDevices.length}`,
                    `Audio devices: ${audioDevices.length}`,
                    '',
                    'Video devices:'
                ];
                
                videoDevices.forEach((device, index) => {
                    deviceInfo.push(`  ${index + 1}. ${device.label || `Camera ${index + 1}`} (${device.deviceId.substring(0, 20)}...)`);
                });
                
                info.innerHTML = '<h3>Available Devices:</h3><pre>' + deviceInfo.join('\n') + '</pre>';
                updateStatus(`Found ${videoDevices.length} video devices`, 'success');
                
            } catch (error) {
                updateStatus(`Device listing failed: ${error.message}`, 'error');
                console.error('Device listing error:', error);
            }
        }

        async function startCamera() {
            try {
                updateStatus('Requesting camera access...');
                
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    throw new Error('Camera API not supported');
                }

                // Stop any existing stream
                if (currentStream) {
                    stopCamera();
                }

                // Request camera access
                currentStream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    },
                    audio: true
                });

                console.log('Stream obtained:', currentStream);
                console.log('Video tracks:', currentStream.getVideoTracks());
                console.log('Audio tracks:', currentStream.getAudioTracks());

                // Set video source
                video.srcObject = currentStream;
                
                // Wait for video to load
                video.onloadedmetadata = () => {
                    console.log('Video metadata loaded');
                    console.log(`Video dimensions: ${video.videoWidth}x${video.videoHeight}`);
                    updateStatus(`✅ Camera active! Resolution: ${video.videoWidth}x${video.videoHeight}`, 'success');
                };

                video.onplay = () => {
                    console.log('Video started playing');
                };

                video.onerror = (e) => {
                    console.error('Video error:', e);
                    updateStatus('Video playback error', 'error');
                };

                // Try to play
                await video.play();
                
            } catch (error) {
                updateStatus(`Camera access failed: ${error.name} - ${error.message}`, 'error');
                console.error('Camera error:', error);
                
                if (error.name === 'NotAllowedError') {
                    updateStatus('❌ Camera permission denied. Please allow camera access and try again.', 'error');
                } else if (error.name === 'NotFoundError') {
                    updateStatus('❌ No camera found. Please connect a camera and try again.', 'error');
                } else if (error.name === 'NotReadableError') {
                    updateStatus('❌ Camera is in use by another application.', 'error');
                }
            }
        }

        function stopCamera() {
            if (currentStream) {
                currentStream.getTracks().forEach(track => {
                    console.log(`Stopping ${track.kind} track`);
                    track.stop();
                });
                currentStream = null;
                video.srcObject = null;
                updateStatus('Camera stopped');
            }
        }

        // Auto-check support on load
        window.onload = () => {
            checkSupport();
        };
    </script>
</body>
</html>
