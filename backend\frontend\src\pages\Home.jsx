import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { streamsAPI } from '../services/api';
import CameraTest from '../components/CameraTest';
import WebRTCDebugger from '../components/WebRTCDebugger';
import './Home.css';

const Home = () => {
  const [streams, setStreams] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchStreams();
  }, []);

  const fetchStreams = async () => {
    try {
      const response = await streamsAPI.getStreams();
      setStreams(response.data);
    } catch (error) {
      setError('Failed to load streams');
      console.error('Error fetching streams:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div className="loading">Loading streams...</div>;
  }

  return (
    <div className="home-container">
      <div className="hero-section">
        <h1>Welcome to LiveStream</h1>
        <p>Discover amazing live streams from creators around the world</p>
        <Link to="/register" className="cta-button">
          Start Streaming Today
        </Link>
      </div>

      <div className="streams-section">
        <div className="demo-notice">
          <h3>🎥 Live Streaming Platform</h3>
          <p>Real-time video streaming is now active! Streamers can broadcast live and viewers can watch in real-time using WebRTC technology.</p>
        </div>

        <CameraTest />

        <WebRTCDebugger />

        <h2>Live Streams</h2>

        {error && <div className="error-message">{error}</div>}
        
        {streams.length === 0 ? (
          <div className="no-streams">
            <p>No live streams at the moment. Check back later!</p>
          </div>
        ) : (
          <div className="streams-grid">
            {streams.map((stream) => (
              <div key={stream.id} className="stream-card">
                <div className="stream-thumbnail">
                  {stream.thumbnail_url ? (
                    <img src={stream.thumbnail_url} alt={stream.title} />
                  ) : (
                    <div className="placeholder-thumbnail">
                      <span>🎥</span>
                    </div>
                  )}
                  <div className="live-badge">LIVE</div>
                </div>
                
                <div className="stream-info">
                  <h3>{stream.title}</h3>
                  <p className="stream-description">{stream.description}</p>
                  <div className="stream-meta">
                    <span className="streamer">by {stream.user.name}</span>
                    <span className="viewers">{stream.viewer_count} viewers</span>
                  </div>
                  <Link to={`/stream/${stream.id}`} className="watch-button">
                    Watch Stream
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Home;
