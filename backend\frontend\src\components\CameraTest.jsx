import React, { useRef, useState } from 'react';

const CameraTest = () => {
  const videoRef = useRef(null);
  const [status, setStatus] = useState('Ready to test camera');
  const [error, setError] = useState('');

  const testBasicCamera = async () => {
    try {
      setError('');
      setStatus('Starting camera test...');
      console.log('Starting basic camera test');

      // Check browser and protocol
      console.log('Current URL:', window.location.href);
      console.log('Protocol:', window.location.protocol);
      console.log('User Agent:', navigator.userAgent);

      // Check if we're on HTTPS or localhost
      const isSecure = window.location.protocol === 'https:' ||
                      window.location.hostname === 'localhost' ||
                      window.location.hostname === '127.0.0.1';

      console.log('Is secure context:', isSecure);
      setStatus(`Protocol: ${window.location.protocol}, Secure: ${isSecure}`);

      // Check if getUserMedia is supported
      if (!navigator.mediaDevices) {
        throw new Error('navigator.mediaDevices not supported - may need HTTPS');
      }

      if (!navigator.mediaDevices.getUserMedia) {
        throw new Error('getUserMedia not supported in this browser');
      }

      console.log('getUserMedia is supported');
      setStatus('getUserMedia supported, requesting camera permissions...');

      // Request camera access with minimal constraints
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: false // Start with video only
      });

      console.log('Stream obtained:', stream);
      console.log('Stream active:', stream.active);
      console.log('Video tracks:', stream.getVideoTracks());

      setStatus(`Stream obtained! Active: ${stream.active}, Video tracks: ${stream.getVideoTracks().length}`);

      if (videoRef.current) {
        console.log('Setting video source');
        videoRef.current.srcObject = stream;
        
        // Try to play
        try {
          await videoRef.current.play();
          console.log('Video is playing');
          setStatus('SUCCESS: Camera is working and video is playing!');
        } catch (playError) {
          console.error('Play error:', playError);
          setStatus(`Stream obtained but play failed: ${playError.message}`);
        }
      } else {
        setError('Video element not found');
      }

    } catch (err) {
      console.error('Camera test error:', err);
      setError(`Camera test failed: ${err.name} - ${err.message}`);
      setStatus('Camera test failed');
    }
  };

  const checkDevices = async () => {
    try {
      setStatus('Checking available devices...');

      if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
        throw new Error('enumerateDevices not supported');
      }

      const devices = await navigator.mediaDevices.enumerateDevices();
      console.log('Available devices:', devices);

      const videoDevices = devices.filter(device => device.kind === 'videoinput');
      const audioDevices = devices.filter(device => device.kind === 'audioinput');

      setStatus(`Found ${videoDevices.length} video devices and ${audioDevices.length} audio devices`);

      videoDevices.forEach((device, index) => {
        console.log(`Video device ${index}:`, device.label || `Camera ${index + 1}`, device.deviceId);
      });

    } catch (err) {
      console.error('Device enumeration error:', err);
      setError(`Device check failed: ${err.message}`);
    }
  };

  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject;
      stream.getTracks().forEach(track => {
        console.log('Stopping track:', track.kind);
        track.stop();
      });
      videoRef.current.srcObject = null;
      setStatus('Camera stopped');
    }
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '20px' }}>
      <h3>Camera Test Component</h3>
      
      <div style={{ marginBottom: '20px' }}>
        <button onClick={checkDevices} style={{ marginRight: '10px', padding: '10px' }}>
          Check Devices
        </button>
        <button onClick={testBasicCamera} style={{ marginRight: '10px', padding: '10px' }}>
          Test Camera
        </button>
        <button onClick={stopCamera} style={{ padding: '10px' }}>
          Stop Camera
        </button>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <strong>Status:</strong> {status}
      </div>

      {error && (
        <div style={{ color: 'red', marginBottom: '20px' }}>
          <strong>Error:</strong> {error}
        </div>
      )}

      <video
        ref={videoRef}
        autoPlay
        muted
        playsInline
        style={{
          width: '400px',
          height: '300px',
          backgroundColor: '#000',
          border: '1px solid #333'
        }}
      />
    </div>
  );
};

export default CameraTest;
