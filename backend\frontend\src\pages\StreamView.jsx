import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { streamsAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import StreamPlayer from '../components/StreamPlayer';
import './StreamView.css';

const StreamView = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { isAuthenticated, user } = useAuth();
  const streamPlayerRef = useRef(null);
  const [stream, setStream] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isWatching, setIsWatching] = useState(false);
  const [isOwner, setIsOwner] = useState(false);

  useEffect(() => {
    fetchStream();
  }, [id]);

  // Debug logging
  useEffect(() => {
    console.log('StreamView state:', {
      streamId: id,
      isOwner,
      isWatching,
      streamStatus: stream?.status,
      shouldStartViewing: isOwner || isWatching
    });
  }, [id, isOwner, isWatching, stream?.status]);

  const fetchStream = async () => {
    try {
      const response = await streamsAPI.getStream(id);
      setStream(response.data);
      setIsOwner(user && response.data.user_id === user.id);
    } catch (error) {
      setError('Stream not found');
      console.error('Error fetching stream:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStartWatching = async () => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    try {
      await streamsAPI.startWatching(id);
      setIsWatching(true);

      // Start the WebRTC viewer connection
      if (streamPlayerRef.current) {
        await streamPlayerRef.current.startViewing();
        console.log('Started WebRTC viewer connection');
      }

      // Refresh stream data to get updated viewer count
      fetchStream();
    } catch (error) {
      console.error('Error starting to watch stream:', error);
      alert('Failed to start watching stream');
    }
  };

  const handleStopWatching = async () => {
    try {
      await streamsAPI.stopWatching(id);
      setIsWatching(false);

      // Stop the WebRTC viewer connection
      if (streamPlayerRef.current) {
        streamPlayerRef.current.stopStreaming();
        console.log('Stopped WebRTC viewer connection');
      }

      // Refresh stream data to get updated viewer count
      fetchStream();
    } catch (error) {
      console.error('Error stopping watching stream:', error);
    }
  };

  if (loading) {
    return <div className="loading">Loading stream...</div>;
  }

  if (error) {
    return (
      <div className="error-container">
        <h2>Stream Not Found</h2>
        <p>{error}</p>
        <button onClick={() => navigate('/')} className="back-button">
          Back to Home
        </button>
      </div>
    );
  }

  return (
    <div className="stream-view-container">
      <div className="stream-player">
        <StreamPlayer
          ref={streamPlayerRef}
          streamId={stream.id}
          isStreamer={isOwner}
          shouldStartViewing={isOwner || isWatching} // Only start viewing if owner (streamer) or actively watching
          onStreamStart={() => {
            // Update stream status to live
            streamsAPI.updateStream(stream.id, { status: 'live' });
            setStream(prev => ({ ...prev, status: 'live' }));
          }}
          onStreamEnd={() => {
            // Update stream status to offline
            streamsAPI.updateStream(stream.id, { status: 'offline' });
            setStream(prev => ({ ...prev, status: 'offline' }));
          }}
        />
      </div>

      <div className="stream-sidebar">
        <div className="stream-info">
          <h3>{stream.title}</h3>
          <p className="streamer-name">by {stream.user.name}</p>
          
          {stream.description && (
            <div className="stream-description">
              <h4>About this stream</h4>
              <p>{stream.description}</p>
            </div>
          )}

          <div className="stream-meta">
            <div className="meta-item">
              <strong>Status:</strong> 
              <span className={`status ${stream.status}`}>
                {stream.status.toUpperCase()}
              </span>
            </div>
            <div className="meta-item">
              <strong>Viewers:</strong> {stream.viewer_count}
            </div>
            {stream.started_at && (
              <div className="meta-item">
                <strong>Started:</strong> {new Date(stream.started_at).toLocaleString()}
              </div>
            )}
          </div>

          {stream.status === 'live' && !isOwner && (
            <div className="stream-actions">
              {isAuthenticated ? (
                isWatching ? (
                  <button onClick={handleStopWatching} className="stop-watching-button">
                    Stop Watching
                  </button>
                ) : (
                  <button onClick={handleStartWatching} className="start-watching-button">
                    Start Watching
                  </button>
                )
              ) : (
                <button onClick={() => navigate('/login')} className="login-prompt">
                  Login to Watch Stream
                </button>
              )}
            </div>
          )}

          {isOwner && (
            <div className="stream-owner-info">
              <p><strong>You are the streamer</strong></p>
              <p>Use the video controls above to manage your stream</p>
            </div>
          )}
        </div>

        {stream.active_viewers && stream.active_viewers.length > 0 && (
          <div className="viewers-list">
            <h4>Current Viewers</h4>
            <div className="viewers">
              {stream.active_viewers.map((viewer) => (
                <div key={viewer.id} className="viewer">
                  {viewer.user.name}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StreamView;
