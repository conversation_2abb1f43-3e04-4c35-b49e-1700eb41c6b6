import React, { useRef, useEffect, useState } from 'react';
import webrtcService from '../services/webrtc';
import { streamsAPI } from '../services/api';
import './StreamPlayer.css';

const StreamPlayer = ({ streamId, isStreamer = false, onStreamStart, onStreamEnd }) => {
  const videoRef = useRef(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState('');
  const [viewerCount, setViewerCount] = useState(0);
  const [debugInfo, setDebugInfo] = useState('');
  const [isVideoClickable, setIsVideoClickable] = useState(false);

  // Debug logging
  console.log('🎥 StreamPlayer rendered with:', {
    streamId,
    isStreamer,
    onStreamStart,
    onStreamEnd,
    isStreaming,
    error,
    debugInfo
  });
  console.log('🔧 TEST: StreamPlayer is working at', new Date().toLocaleTimeString());

  useEffect(() => {
    console.log('StreamPlayer useEffect triggered:', { streamId, isStreamer });

    if (streamId && !isStreamer) {
      console.log('Setting up viewer view');
      setupViewerView();
    } else if (streamId && isStreamer) {
      console.log('Setting up streamer view');
      setupStreamerView();
    }

    return () => {
      console.log('StreamPlayer cleanup');
      cleanup();
    };
  }, [streamId, isStreamer]);

  const setupStreamerView = async () => {
    // Streamers don't need automatic setup
    // Everything happens when they click "Start Streaming"
    setDebugInfo('Ready to start streaming');
  };

  const setupViewerView = async () => {
    try {
      if (!webrtcService.isSupported()) {
        setError('WebRTC is not supported in your browser');
        return;
      }

      setDebugInfo('Setting up viewer...');
      console.log('=== SETTING UP VIEWER ===');

      // Set up stream callback
      webrtcService.onStream((stream) => {
        if (stream) {
          console.log('Received stream in viewer:', stream);
          console.log('Stream tracks:', stream.getTracks().map(t => ({
            kind: t.kind,
            enabled: t.enabled,
            readyState: t.readyState,
            settings: t.getSettings()
          })));

          setDebugInfo('Stream received! Setting up video...');
          if (videoRef.current) {
            // Stop any existing stream first
            if (videoRef.current.srcObject) {
              const existingStream = videoRef.current.srcObject;
              existingStream.getTracks().forEach(track => track.stop());
            }

            // Force video element properties
            videoRef.current.srcObject = stream;
            videoRef.current.muted = false; // Viewers should hear audio
            videoRef.current.autoplay = true;
            videoRef.current.playsInline = true;

            // Add detailed video element debugging
            videoRef.current.onloadedmetadata = () => {
              console.log('Viewer: Video metadata loaded');
              console.log('Video dimensions:', videoRef.current.videoWidth, 'x', videoRef.current.videoHeight);
              console.log('Video duration:', videoRef.current.duration);
              console.log('Video ready state:', videoRef.current.readyState);

              // Force video element to use proper dimensions if they're tiny
              if (videoRef.current.videoWidth <= 2 || videoRef.current.videoHeight <= 2) {
                console.warn('Video dimensions are too small, forcing minimum size and checking stream');

                // Check if the stream has video tracks with proper settings
                const stream = videoRef.current.srcObject;
                const videoTracks = stream.getVideoTracks();
                if (videoTracks.length > 0) {
                  const track = videoTracks[0];
                  const settings = track.getSettings();
                  console.log('Video track settings on tiny video:', settings);

                  // If track has proper dimensions, force video element size
                  if (settings.width && settings.height) {
                    videoRef.current.style.width = `${settings.width}px`;
                    videoRef.current.style.height = `${settings.height}px`;
                    setDebugInfo(`Video track has ${settings.width}x${settings.height} but element shows ${videoRef.current.videoWidth}x${videoRef.current.videoHeight} - forced element size`);
                  } else {
                    videoRef.current.style.width = '640px';
                    videoRef.current.style.height = '480px';
                    setDebugInfo(`Video loaded but dimensions too small: ${videoRef.current.videoWidth}x${videoRef.current.videoHeight} - forced to 640x480`);
                  }
                } else {
                  setDebugInfo('No video tracks found in stream');
                }
              } else {
                setDebugInfo(`Video loaded: ${videoRef.current.videoWidth}x${videoRef.current.videoHeight}`);
              }
            };

            videoRef.current.onplay = () => {
              console.log('Viewer: Video started playing');
              setDebugInfo('Video is playing!');
            };

            videoRef.current.onplaying = () => {
              console.log('Viewer: Video is actively playing');
              setIsStreaming(true);
              setDebugInfo('Now watching live stream!');
            };

            videoRef.current.onerror = (e) => {
              console.error('Viewer: Video error:', e);
              setDebugInfo('Video error occurred');
            };

            videoRef.current.onstalled = () => {
              console.warn('Viewer: Video stalled');
              setDebugInfo('Video stalled');
            };

            videoRef.current.onwaiting = () => {
              console.log('Viewer: Video waiting for data');
              setDebugInfo('Video waiting for data');
            };

            // Wait a bit before playing to avoid interruption
            setTimeout(() => {
              if (videoRef.current && videoRef.current.srcObject === stream) {
                console.log('Attempting to play video...');
                videoRef.current.play().then(() => {
                  console.log('Viewer: Video play() succeeded');
                }).catch(e => {
                  console.warn('Video play failed:', e);
                  setDebugInfo('Stream received but autoplay failed - click video to play');
                  setError('Click the video to start playback (browser autoplay policy)');
                  setIsVideoClickable(true);

                  // Try to play manually after user interaction
                  videoRef.current.onclick = () => {
                    videoRef.current.play().then(() => {
                      console.log('Manual play succeeded');
                      setDebugInfo('Video playing after manual click');
                      setError(''); // Clear the error
                      setIsVideoClickable(false);
                    }).catch(err => {
                      console.error('Manual play failed:', err);
                      setError(`Manual play failed: ${err.message}`);
                    });
                  };
                });
              }
            }, 100);
          }
        } else {
          console.log('Stream connection lost');
          setDebugInfo('Stream connection lost');
          setIsStreaming(false);
        }
      });

      // Start watching the stream
      setDebugInfo('Connecting to stream...');
      console.log('Attempting to watch stream:', streamId);
      await webrtcService.watchStream(streamId);
      setDebugInfo('Connected! Waiting for video stream...');
      console.log('WebRTC watch stream setup complete');

    } catch (error) {
      console.error('Error setting up viewer:', error);
      setError(`Failed to setup viewer: ${error.message}`);
      setDebugInfo('');
    }
  };

  const simpleCameraTest = async () => {
    console.log('=== SIMPLE CAMERA TEST START ===');
    try {
      setError('');
      setDebugInfo('Simple test: requesting camera...');

      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      console.log('Simple test: got stream', stream);

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.muted = true;
        await videoRef.current.play();
        setDebugInfo('Simple test: SUCCESS!');
        setIsStreaming(true);
        console.log('Simple test: SUCCESS!');
      }
    } catch (error) {
      console.error('Simple test failed:', error);
      setError(`Simple test failed: ${error.message}`);
      setDebugInfo(`Simple test failed: ${error.message}`);
    }
    console.log('=== SIMPLE CAMERA TEST END ===');
  };

  const testVideoElement = () => {
    console.log('=== VIDEO ELEMENT TEST ===');
    console.log('videoRef:', videoRef);
    console.log('videoRef.current:', videoRef.current);
    console.log('Video element details:', {
      exists: !!videoRef.current,
      tagName: videoRef.current?.tagName,
      id: videoRef.current?.id,
      className: videoRef.current?.className,
      parentElement: videoRef.current?.parentElement,
      style: videoRef.current?.style?.cssText
    });

    // Also check if buttons exist in DOM
    const buttons = document.querySelectorAll('.stream-buttons button');
    console.log('Buttons in DOM:', buttons.length);
    buttons.forEach((btn, index) => {
      console.log(`Button ${index}:`, btn.textContent, btn.style.cssText);
    });

    setDebugInfo(`Video element exists: ${!!videoRef.current}, Buttons found: ${buttons.length}`);
  };

  const testCamera = async () => {
    try {
      setError('');
      setDebugInfo('Testing camera access...');
      console.log('Testing camera access...');

      // Check if getUserMedia is available
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera access not supported in this browser');
      }

      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          frameRate: { ideal: 30 }
        },
        audio: true
      });

      console.log('Camera test successful! Stream:', stream);
      console.log('Video tracks:', stream.getVideoTracks());
      console.log('Audio tracks:', stream.getAudioTracks());

      setDebugInfo(`Camera test successful! Video tracks: ${stream.getVideoTracks().length}, Audio tracks: ${stream.getAudioTracks().length}`);

      if (videoRef.current) {
        console.log('Setting video source for test');
        videoRef.current.srcObject = stream;

        // Add event listeners for debugging
        videoRef.current.onloadedmetadata = () => {
          console.log('Video metadata loaded');
          setDebugInfo('Video metadata loaded, attempting to play...');
        };

        videoRef.current.onplay = () => {
          console.log('Video started playing');
          setDebugInfo('Video is playing!');
        };

        try {
          await videoRef.current.play();
          console.log('Video play() succeeded');
        } catch (playError) {
          console.error('Video play() failed:', playError);
          setDebugInfo(`Video play failed: ${playError.message}`);
        }
      }

      // Stop test stream after 5 seconds
      setTimeout(() => {
        console.log('Stopping test stream');
        stream.getTracks().forEach(track => {
          console.log('Stopping track:', track.kind);
          track.stop();
        });
        setDebugInfo('Camera test completed');
        if (videoRef.current) {
          videoRef.current.srcObject = null;
        }
      }, 5000);

    } catch (error) {
      console.error('Camera test failed:', error);
      setError(`Camera test failed: ${error.message}`);
      setDebugInfo('');
    }
  };

  const directCameraTest = async () => {
    try {
      console.log('Direct camera test starting...');
      setError('');
      setDebugInfo('Direct camera test...');

      // Use the exact same constraints as our working test
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 }
        },
        audio: true
      });

      console.log('Direct camera test - stream obtained:', stream);
      console.log('Video tracks:', stream.getVideoTracks());
      console.log('Audio tracks:', stream.getAudioTracks());

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.muted = true; // Prevent feedback

        videoRef.current.onloadedmetadata = () => {
          console.log('Direct test - metadata loaded');
          console.log(`Video dimensions: ${videoRef.current.videoWidth}x${videoRef.current.videoHeight}`);
          setDebugInfo(`Direct test - video loaded: ${videoRef.current.videoWidth}x${videoRef.current.videoHeight}`);
        };

        videoRef.current.onplay = () => {
          console.log('Direct test - video playing');
          setDebugInfo('Direct test - video playing!');
        };

        await videoRef.current.play();
        setIsStreaming(true);
      }

    } catch (error) {
      console.error('Direct camera test failed:', error);
      setError(`Direct test failed: ${error.message}`);
    }
  };

  const startStreaming = async () => {
    console.log('=== START STREAMING FUNCTION CALLED ===');
    console.log('Function context:', {
      streamId,
      isStreamer,
      videoRef: videoRef.current,
      videoRefExists: !!videoRef.current
    });

    try {
      setError('');
      setDebugInfo('Starting streaming process...');
      console.log('=== ATTEMPTING CAMERA ACCESS ===');

      // Check browser support first
      if (!navigator.mediaDevices) {
        throw new Error('navigator.mediaDevices not supported');
      }

      if (!navigator.mediaDevices.getUserMedia) {
        throw new Error('getUserMedia not supported');
      }

      console.log('Browser supports camera API');
      setDebugInfo('Browser supports camera, requesting access...');

      // Request camera with detailed logging
      console.log('Calling getUserMedia...');
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280, min: 640 },
          height: { ideal: 720, min: 480 },
          frameRate: { ideal: 30, min: 15 }
        },
        audio: true
      });

      console.log('SUCCESS: Got camera stream!', stream);
      console.log('Stream details:', {
        id: stream.id,
        active: stream.active,
        videoTracks: stream.getVideoTracks().length,
        audioTracks: stream.getAudioTracks().length
      });

      // Log video track details for streamer
      stream.getVideoTracks().forEach((track, index) => {
        console.log(`Streamer video track ${index}:`, {
          kind: track.kind,
          enabled: track.enabled,
          readyState: track.readyState,
          settings: track.getSettings(),
          constraints: track.getConstraints()
        });
      });

      setDebugInfo(`SUCCESS: Camera stream obtained! Video tracks: ${stream.getVideoTracks().length}`);

      // Check video element
      console.log('Checking video element:', {
        videoRef: videoRef.current,
        videoRefType: typeof videoRef.current,
        videoRefTagName: videoRef.current?.tagName
      });

      if (videoRef.current) {
        console.log('Setting video source...');
        videoRef.current.srcObject = stream;
        videoRef.current.muted = true;

        console.log('Attempting to play video...');
        try {
          await videoRef.current.play();
          console.log('SUCCESS: Video is playing!');
          setIsStreaming(true);
          setDebugInfo('SUCCESS: Camera is working! Video playing.');

          // Update stream status to live in the backend
          try {
            await streamsAPI.updateStream(streamId, { status: 'live' });
            if (onStreamStart) onStreamStart();
            console.log('Stream status updated to live');
            setDebugInfo('Stream is now live!');
          } catch (apiError) {
            console.error('Error updating stream status:', apiError);
            setDebugInfo('Camera working but API update failed');
          }
        } catch (playError) {
          console.error('Video play failed:', playError);
          setDebugInfo(`Video play failed: ${playError.message}`);
          setError(`Video play failed: ${playError.message}`);
        }
      } else {
        console.error('ERROR: Video element not found!');
        setError('Video element not found');
        setDebugInfo('ERROR: Video element not found');
      }

      // Now add WebRTC setup for peer connections
      console.log('=== SETTING UP WEBRTC FOR VIEWERS ===');

      // Set up WebRTC callbacks for streamers
      webrtcService.onViewerJoin((data) => {
        console.log('Viewer joined:', data);
        setViewerCount(prev => prev + 1);
        // Create peer connection for new viewer
        webrtcService.createPeerForViewer(data.viewerId);
      });

      webrtcService.onViewerLeave((data) => {
        console.log('Viewer left:', data);
        setViewerCount(prev => Math.max(0, prev - 1));
      });

      // Initialize WebRTC service with the stream
      try {
        await webrtcService.initSocket();
        webrtcService.localStream = stream; // Set the stream for WebRTC
        webrtcService.isStreamer = true; // Mark as streamer
        webrtcService.streamId = streamId; // Set stream ID

        console.log('WebRTC service configured:', {
          hasLocalStream: !!webrtcService.localStream,
          isStreamer: webrtcService.isStreamer,
          streamId: webrtcService.streamId,
          socketConnected: !!webrtcService.socket
        });

        // Emit start-stream to signaling server
        if (webrtcService.socket) {
          webrtcService.socket.emit('start-stream', { streamId });
          console.log('Notified signaling server about stream start');
        }
      } catch (webrtcError) {
        console.warn('WebRTC setup failed, but camera still works:', webrtcError);
      }

      console.log('=== CAMERA AND WEBRTC SETUP COMPLETE ===');

    } catch (error) {
      console.error('=== CAMERA ACCESS FAILED ===');
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
      setError(`Camera failed: ${error.name} - ${error.message}`);
      setDebugInfo(`Camera failed: ${error.name} - ${error.message}`);
    }
  };

  const stopStreaming = async () => {
    webrtcService.stopStreaming();
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
    setIsStreaming(false);

    // Update stream status to offline in the backend
    try {
      await streamsAPI.updateStream(streamId, { status: 'offline' });
      if (onStreamEnd) onStreamEnd();
    } catch (apiError) {
      console.error('Error updating stream status:', apiError);
    }
  };

  const cleanup = () => {
    if (isStreamer) {
      webrtcService.stopStreaming();
    } else {
      webrtcService.stopWatching();
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
    setIsStreaming(false);
  };

  // Debug render conditions
  const shouldShowButtons = !isStreaming && !error.trim();
  const shouldShowStreamerControls = shouldShowButtons && isStreamer;
  console.log('Render conditions:', {
    isStreaming,
    error: error,
    errorRaw: JSON.stringify(error),
    errorType: typeof error,
    errorLength: error?.length,
    errorTruthy: !!error,
    shouldShowButtons,
    shouldShowStreamerControls,
    isStreamer,
    notIsStreaming: !isStreaming,
    notError: !error
  });

  return (
    <div className="stream-player">
      <div className="video-container">
        <video
          ref={videoRef}
          autoPlay
          muted={isStreamer} // Mute own stream to prevent feedback
          playsInline
          controls={false}
          className={`video-element ${isVideoClickable ? 'clickable' : ''}`}
          onLoadedMetadata={() => {
            console.log('Video metadata loaded');
            console.log('Video dimensions:', videoRef.current?.videoWidth, 'x', videoRef.current?.videoHeight);
            console.log('Video duration:', videoRef.current?.duration);
            console.log('Video ready state:', videoRef.current?.readyState);
            console.log('Video src object:', videoRef.current?.srcObject);
            if (videoRef.current) {
              videoRef.current.play().catch(e => console.warn('Video play failed:', e));
            }
          }}
          onCanPlay={() => {
            console.log('Video can play');
            setDebugInfo('Video can play - ready for playback');
          }}
          onPlay={() => {
            console.log('Video started playing');
            setDebugInfo('Video is now playing!');
          }}
          onPause={() => {
            console.log('Video paused');
          }}
          onWaiting={() => {
            console.log('Video waiting for data');
            setDebugInfo('Video waiting for data...');
          }}
          onError={(e) => {
            console.error('Video error:', e);
            console.error('Video error details:', e.target.error);
            setError(`Video playback error: ${e.target.error?.message || 'Unknown error'}`);
          }}
        />

        {(!isStreaming && !error.trim()) && (
          <div className="video-placeholder">
            {isStreamer ? (
              <div className="streamer-controls">
                <h3>Ready to start streaming?</h3>
                <p>Click the button below to start your live stream</p>
                <div className="stream-buttons">
                  <button onClick={testVideoElement} className="test-camera-btn">
                    Test Video Element
                  </button>
                  <button
                    onClick={() => {
                      console.log('SIMPLE CAMERA TEST CLICKED!');
                      simpleCameraTest();
                    }}
                    className="test-camera-btn"
                  >
                    Simple Camera Test
                  </button>
                  <button onClick={testCamera} className="test-camera-btn">
                    Test Camera
                  </button>
                  <button onClick={directCameraTest} className="test-camera-btn">
                    Direct Camera Test
                  </button>
                  <button
                    onClick={() => {
                      console.log('START STREAMING BUTTON CLICKED!');
                      startStreaming();
                    }}
                    className="start-stream-btn"
                  >
                    Start Streaming
                  </button>
                </div>
                {debugInfo && (
                  <div className="debug-info">
                    <p>{debugInfo}</p>
                  </div>
                )}
              </div>
            ) : (
              <div className="viewer-waiting">
                <h3>Connecting to Live Stream...</h3>
                <p>Establishing connection with the streamer.</p>
                <p>Please wait while we set up the video feed.</p>
                {debugInfo && (
                  <div className="debug-info">
                    <p>{debugInfo}</p>
                  </div>
                )}
                {debugInfo.includes('autoplay failed') && (
                  <button
                    onClick={() => {
                      if (videoRef.current) {
                        videoRef.current.play().then(() => {
                          console.log('Manual play button succeeded');
                          setDebugInfo('Video playing after manual click');
                          setIsStreaming(true);
                        }).catch(err => {
                          console.error('Manual play button failed:', err);
                        });
                      }
                    }}
                    className="play-btn"
                    style={{
                      background: '#4caf50',
                      color: 'white',
                      border: 'none',
                      padding: '10px 20px',
                      borderRadius: '5px',
                      cursor: 'pointer',
                      marginTop: '10px'
                    }}
                  >
                    ▶ Click to Play Stream
                  </button>
                )}
                {!isStreamer && (
                  <div className="viewer-note">
                    <p><strong>Note:</strong> Live streaming between users is now active.
                    If you don't see video, the streamer may not be broadcasting or your browser may have blocked autoplay.</p>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {error && (
          <div className="video-error">
            <h3>Error</h3>
            <p>{error}</p>
            {isStreamer && (
              <button onClick={startStreaming} className="retry-btn">
                Try Again
              </button>
            )}
          </div>
        )}

        {isStreaming && (
          <div className="video-overlay">
            <div className="stream-status">
              <span className="live-indicator">● LIVE</span>
              {isStreamer && (
                <span className="viewer-count">{viewerCount} viewers</span>
              )}
            </div>
            
            {isStreamer && (
              <div className="stream-controls">
                <button onClick={stopStreaming} className="stop-stream-btn">
                  Stop Stream
                </button>
              </div>
            )}
          </div>
        )}

        {/* ALWAYS show debug buttons for streamers - for testing */}
        {isStreamer && (
          <div className="debug-controls" style={{
            position: 'absolute',
            bottom: '10px',
            left: '10px',
            background: 'rgba(0,0,0,0.8)',
            padding: '10px',
            borderRadius: '5px',
            zIndex: 1000
          }}>
            <h4 style={{color: 'white', margin: '0 0 10px 0', fontSize: '14px'}}>Debug Controls</h4>
            <div style={{display: 'flex', gap: '5px', flexWrap: 'wrap'}}>
              <button
                onClick={() => {
                  console.log('DEBUG: Test Video Element clicked');
                  testVideoElement();
                }}
                style={{padding: '5px 10px', fontSize: '12px'}}
              >
                Test Video
              </button>
              <button
                onClick={() => {
                  console.log('DEBUG: Simple Camera Test clicked');
                  simpleCameraTest();
                }}
                style={{padding: '5px 10px', fontSize: '12px'}}
              >
                Simple Camera
              </button>
              <button
                onClick={() => {
                  console.log('DEBUG: Start Streaming clicked');
                  startStreaming();
                }}
                style={{padding: '5px 10px', fontSize: '12px'}}
              >
                Start Stream
              </button>
            </div>
            {debugInfo && (
              <div style={{color: 'white', fontSize: '12px', marginTop: '5px'}}>
                {debugInfo}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default StreamPlayer;
