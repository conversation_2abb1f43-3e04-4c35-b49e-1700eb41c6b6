# LiveStream Signaling Server

WebRTC signaling server for the LiveStream platform, enabling real-time peer-to-peer video streaming between streamers and viewers.

## Features

- **Real-time signaling** for WebRTC connections
- **Multi-viewer support** for each stream
- **Automatic cleanup** when streams end or users disconnect
- **CORS enabled** for frontend integration
- **Live statistics** endpoint

## Installation

```bash
npm install
```

## Usage

### Development
```bash
npm run dev
```

### Production
```bash
npm start
```

The server will start on port 8001 by default.

## API Endpoints

### HTTP Endpoints

- `GET /` - Server status and statistics

### WebSocket Events

#### Streamer Events
- `start-stream` - Start broadcasting a stream
- `stop-stream` - Stop broadcasting a stream
- `signal` - WebRTC signaling data

#### Viewer Events
- `watch-stream` - Start watching a stream
- `stop-watching` - Stop watching a stream
- `signal` - WebRTC signaling data

#### Server Events
- `viewer-joined` - Notifies streamer when viewer joins
- `viewer-left` - Notifies streamer when viewer leaves
- `viewer-wants-stream` - Requests streamer to create peer connection
- `stream-ended` - Notifies viewers when stream ends
- `signal` - WebRTC signaling data relay

## Architecture

```
Streamer (Browser) <---> Signaling Server <---> Viewer (Browser)
                              |
                         [Socket.io Events]
                              |
                    [WebRTC Peer Connection Setup]
                              |
                         Direct P2P Video
                    Streamer <-----------> Viewer
```

## Configuration

Environment variables:
- `PORT` - Server port (default: 8001)

## Integration

The signaling server works with the LiveStream frontend application. Make sure:

1. Frontend WebRTC service points to `http://localhost:8001`
2. CORS origins include your frontend URL
3. Both servers are running simultaneously

## Development

The server uses:
- **Express.js** for HTTP server
- **Socket.io** for WebSocket communication
- **CORS** for cross-origin requests

## Monitoring

Visit `http://localhost:8001` to see:
- Active streams count
- Total viewers count
- Server status

## Production Deployment

For production deployment:

1. Set appropriate environment variables
2. Use a process manager like PM2
3. Configure reverse proxy (nginx)
4. Enable SSL/TLS for secure WebRTC
5. Consider using TURN servers for NAT traversal

## Troubleshooting

- **Connection refused**: Check if server is running on correct port
- **CORS errors**: Verify frontend URL is in CORS origins
- **WebRTC fails**: May need TURN servers for complex network setups
