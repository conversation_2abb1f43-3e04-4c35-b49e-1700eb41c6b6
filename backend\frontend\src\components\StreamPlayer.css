.stream-player {
  width: 100%;
  height: 100%;
}

.video-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  aspect-ratio: 16/9;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background-color: #000;
  display: block;
  min-width: 320px;
  min-height: 240px;
}

.video-element.clickable {
  cursor: pointer;
  border: 2px solid #007bff;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { border-color: #007bff; }
  50% { border-color: #0056b3; }
  100% { border-color: #007bff; }
}

.video-placeholder,
.video-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #1a1a1a, #2a2a2a);
  color: white;
  text-align: center;
  padding: 2rem;
}

.streamer-controls h3,
.viewer-waiting h3,
.video-error h3 {
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.streamer-controls p,
.viewer-waiting p,
.video-error p {
  margin-bottom: 2rem;
  opacity: 0.8;
  max-width: 400px;
}

.stream-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.start-stream-btn,
.test-camera-btn,
.retry-btn {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 6px;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
}

.test-camera-btn {
  background: linear-gradient(45deg, #4caf50, #66bb6a);
}

.start-stream-btn:hover,
.test-camera-btn:hover,
.retry-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

.test-camera-btn:hover {
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.debug-info {
  margin-top: 1rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  font-size: 0.9rem;
  color: #e0e0e0;
}

.viewer-note {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.3);
  border-radius: 4px;
  font-size: 0.85rem;
  color: #bbdefb;
  text-align: left;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 1rem;
}

.stream-status {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  pointer-events: none;
}

.live-indicator {
  background: rgba(255, 0, 0, 0.9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: bold;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.live-indicator::before {
  content: '';
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.viewer-count {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
}

.stream-controls {
  display: flex;
  justify-content: center;
  pointer-events: auto;
}

.stop-stream-btn {
  background: rgba(244, 67, 54, 0.9);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
}

.stop-stream-btn:hover {
  background: rgba(244, 67, 54, 1);
}

.viewer-waiting {
  animation: fadeInOut 2s infinite;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

.video-error {
  background: linear-gradient(45deg, #d32f2f, #f44336);
}

.video-error h3 {
  color: #ffebee;
}

.video-error p {
  color: #ffcdd2;
}

/* Responsive design */
@media (max-width: 768px) {
  .video-container {
    aspect-ratio: 16/9;
  }
  
  .video-placeholder,
  .video-error {
    padding: 1rem;
  }
  
  .streamer-controls h3,
  .viewer-waiting h3,
  .video-error h3 {
    font-size: 1.2rem;
  }
  
  .start-stream-btn,
  .retry-btn {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
  
  .video-overlay {
    padding: 0.5rem;
  }
  
  .live-indicator,
  .viewer-count {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
}
