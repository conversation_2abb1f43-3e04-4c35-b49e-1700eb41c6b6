import React, { useState, useRef, useEffect } from 'react';
import webrtcService from '../services/webrtc';

const WebRTCDebugger = () => {
  const [logs, setLogs] = useState([]);
  const [isStreamer, setIsStreamer] = useState(false);
  const [streamId, setStreamId] = useState('debug-stream-123');
  const [connectionState, setConnectionState] = useState('disconnected');
  const [iceState, setIceState] = useState('new');
  const [signalingState, setSignalingState] = useState('stable');
  const [hasLocalStream, setHasLocalStream] = useState(false);
  const [hasRemoteStream, setHasRemoteStream] = useState(false);
  const [viewerCount, setViewerCount] = useState(0);
  const videoRef = useRef(null);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { timestamp, message, type }]);
    console.log(`[WebRTC Debug ${type.toUpperCase()}] ${message}`);
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const testSignalingServer = async () => {
    try {
      addLog('Testing signaling server connection...', 'info');
      const response = await fetch('http://localhost:8001');
      const data = await response.json();
      addLog(`Signaling server response: ${JSON.stringify(data)}`, 'success');
    } catch (error) {
      addLog(`Signaling server error: ${error.message}`, 'error');
    }
  };

  const startAsStreamer = async () => {
    try {
      addLog('Starting as streamer...', 'info');
      setIsStreamer(true);
      
      // Initialize WebRTC service
      await webrtcService.initSocket();
      addLog('Socket initialized', 'success');

      // Set up callbacks
      webrtcService.onViewerJoin((data) => {
        addLog(`Viewer joined: ${data.viewerId}`, 'success');
        setViewerCount(prev => prev + 1);
      });

      webrtcService.onViewerLeave((data) => {
        addLog(`Viewer left: ${data.viewerId}`, 'info');
        setViewerCount(prev => Math.max(0, prev - 1));
      });

      // Start streaming
      const success = await webrtcService.startStreaming(streamId);
      if (success) {
        addLog('Streaming started successfully!', 'success');
        setHasLocalStream(true);
        
        // Display local stream
        if (videoRef.current && webrtcService.localStream) {
          videoRef.current.srcObject = webrtcService.localStream;
          videoRef.current.muted = true;
          await videoRef.current.play();
          addLog('Local video displayed', 'success');
        }
      } else {
        addLog('Failed to start streaming', 'error');
      }
    } catch (error) {
      addLog(`Streamer setup error: ${error.message}`, 'error');
    }
  };

  const startAsViewer = async () => {
    try {
      addLog('Starting as viewer...', 'info');
      setIsStreamer(false);
      
      // Initialize WebRTC service
      await webrtcService.initSocket();
      addLog('Socket initialized', 'success');

      // Set up stream callback
      webrtcService.onStream((stream) => {
        if (stream) {
          addLog('Received remote stream!', 'success');
          setHasRemoteStream(true);
          
          // Display remote stream
          if (videoRef.current) {
            videoRef.current.srcObject = stream;
            videoRef.current.muted = false;
            videoRef.current.play().then(() => {
              addLog('Remote video playing', 'success');
            }).catch(e => {
              addLog(`Remote video play error: ${e.message}`, 'error');
            });
          }
        } else {
          addLog('Remote stream lost', 'warning');
          setHasRemoteStream(false);
        }
      });

      // Start watching
      const success = await webrtcService.watchStream(streamId);
      if (success) {
        addLog('Started watching stream', 'success');
      } else {
        addLog('Failed to watch stream', 'error');
      }
    } catch (error) {
      addLog(`Viewer setup error: ${error.message}`, 'error');
    }
  };

  const stopStreaming = () => {
    try {
      webrtcService.stopStreaming();
      setHasLocalStream(false);
      setHasRemoteStream(false);
      setViewerCount(0);
      if (videoRef.current) {
        videoRef.current.srcObject = null;
      }
      addLog('Streaming stopped', 'info');
    } catch (error) {
      addLog(`Stop error: ${error.message}`, 'error');
    }
  };

  // Monitor connection states
  useEffect(() => {
    const interval = setInterval(() => {
      if (webrtcService.peerConnection) {
        setConnectionState(webrtcService.peerConnection.connectionState);
        setIceState(webrtcService.peerConnection.iceConnectionState);
        setSignalingState(webrtcService.peerConnection.signalingState);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div style={{ padding: '20px', border: '2px solid #007bff', margin: '20px', borderRadius: '8px' }}>
      <h3>🔧 WebRTC Debugger</h3>
      
      {/* Controls */}
      <div style={{ marginBottom: '20px', display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
        <button onClick={testSignalingServer} style={{ padding: '8px 16px' }}>
          Test Signaling Server
        </button>
        <button onClick={startAsStreamer} style={{ padding: '8px 16px', backgroundColor: '#28a745', color: 'white', border: 'none' }}>
          Start as Streamer
        </button>
        <button onClick={startAsViewer} style={{ padding: '8px 16px', backgroundColor: '#17a2b8', color: 'white', border: 'none' }}>
          Start as Viewer
        </button>
        <button onClick={stopStreaming} style={{ padding: '8px 16px', backgroundColor: '#dc3545', color: 'white', border: 'none' }}>
          Stop
        </button>
        <button onClick={clearLogs} style={{ padding: '8px 16px' }}>
          Clear Logs
        </button>
      </div>

      {/* Status */}
      <div style={{ marginBottom: '20px', display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>
        <div style={{ padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
          <strong>Role:</strong> {isStreamer ? 'Streamer' : 'Viewer'}
        </div>
        <div style={{ padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
          <strong>Stream ID:</strong> {streamId}
        </div>
        <div style={{ padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
          <strong>Connection:</strong> {connectionState}
        </div>
        <div style={{ padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
          <strong>ICE:</strong> {iceState}
        </div>
        <div style={{ padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
          <strong>Signaling:</strong> {signalingState}
        </div>
        <div style={{ padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
          <strong>Local Stream:</strong> {hasLocalStream ? '✅' : '❌'}
        </div>
        <div style={{ padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
          <strong>Remote Stream:</strong> {hasRemoteStream ? '✅' : '❌'}
        </div>
        <div style={{ padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
          <strong>Viewers:</strong> {viewerCount}
        </div>
      </div>

      {/* Video */}
      <div style={{ marginBottom: '20px' }}>
        <video
          ref={videoRef}
          autoPlay
          playsInline
          style={{
            width: '400px',
            height: '300px',
            backgroundColor: '#000',
            border: '2px solid #333',
            borderRadius: '4px'
          }}
        />
      </div>

      {/* Logs */}
      <div style={{ height: '300px', overflowY: 'auto', border: '1px solid #ccc', padding: '10px', backgroundColor: '#f8f9fa' }}>
        <h4>Debug Logs:</h4>
        {logs.map((log, index) => (
          <div key={index} style={{ 
            marginBottom: '5px', 
            fontSize: '12px',
            color: log.type === 'error' ? '#dc3545' : log.type === 'success' ? '#28a745' : log.type === 'warning' ? '#ffc107' : '#333'
          }}>
            <span style={{ fontWeight: 'bold' }}>[{log.timestamp}]</span> {log.message}
          </div>
        ))}
      </div>
    </div>
  );
};

export default WebRTCDebugger;
