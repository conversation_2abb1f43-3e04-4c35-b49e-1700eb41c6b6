.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.dashboard-header h1 {
  color: #333;
  margin: 0;
}

.create-stream-btn {
  background-color: #ff6b6b;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: bold;
  transition: background-color 0.3s;
}

.create-stream-btn:hover {
  background-color: #ff5252;
}

.dashboard-section h2 {
  color: #333;
  margin-bottom: 1.5rem;
}

.no-streams {
  text-align: center;
  padding: 3rem;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.create-first-stream {
  display: inline-block;
  background-color: #ff6b6b;
  color: white;
  padding: 1rem 2rem;
  border-radius: 6px;
  text-decoration: none;
  margin-top: 1rem;
  font-weight: bold;
}

.streams-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.streams-table table {
  width: 100%;
  border-collapse: collapse;
}

.streams-table th,
.streams-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

.streams-table th {
  background-color: #f8f9fa;
  font-weight: bold;
  color: #333;
}

.stream-title strong {
  display: block;
  margin-bottom: 0.25rem;
}

.stream-desc {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
  text-transform: uppercase;
}

.status-badge.live {
  background-color: #4caf50;
  color: white;
}

.status-badge.offline {
  background-color: #9e9e9e;
  color: white;
}

.status-badge.ended {
  background-color: #f44336;
  color: white;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.action-buttons button,
.action-buttons a {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  text-decoration: none;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.stream-btn {
  background-color: #ff6b6b;
  color: white;
  font-weight: bold;
}

.stream-btn:hover {
  background-color: #ff5252;
  text-decoration: none;
}

.view-btn {
  background-color: #2196f3;
  color: white;
}

.delete-btn {
  background-color: #f44336;
  color: white;
}

.toggle-btn:hover,
.view-btn:hover,
.delete-btn:hover {
  opacity: 0.8;
}

.loading {
  text-align: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: #666;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 2rem;
  border: 1px solid #ffcdd2;
}
