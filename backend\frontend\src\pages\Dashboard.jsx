import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { streamsAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import './Dashboard.css';

const Dashboard = () => {
  const [myStreams, setMyStreams] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const { user } = useAuth();

  useEffect(() => {
    fetchMyStreams();
  }, []);

  const fetchMyStreams = async () => {
    try {
      const response = await streamsAPI.getMyStreams();
      setMyStreams(response.data);
    } catch (error) {
      setError('Failed to load your streams');
      console.error('Error fetching streams:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteStream = async (streamId) => {
    if (window.confirm('Are you sure you want to delete this stream?')) {
      try {
        await streamsAPI.deleteStream(streamId);
        setMyStreams(myStreams.filter(stream => stream.id !== streamId));
      } catch (error) {
        console.error('Error deleting stream:', error);
        alert('Failed to delete stream');
      }
    }
  };

  const handleToggleStream = async (stream) => {
    try {
      const newStatus = stream.status === 'live' ? 'offline' : 'live';
      await streamsAPI.updateStream(stream.id, { status: newStatus });
      
      setMyStreams(myStreams.map(s => 
        s.id === stream.id ? { ...s, status: newStatus } : s
      ));
    } catch (error) {
      console.error('Error updating stream status:', error);
      alert('Failed to update stream status');
    }
  };

  if (loading) {
    return <div className="loading">Loading your dashboard...</div>;
  }

  return (
    <div className="dashboard-container">
      <div className="dashboard-header">
        <h1>Welcome back, {user?.name}!</h1>
        <Link to="/create-stream" className="create-stream-btn">
          Create New Stream
        </Link>
      </div>

      {error && <div className="error-message">{error}</div>}

      <div className="dashboard-section">
        <h2>Your Streams</h2>
        
        {myStreams.length === 0 ? (
          <div className="no-streams">
            <p>You haven't created any streams yet.</p>
            <Link to="/create-stream" className="create-first-stream">
              Create Your First Stream
            </Link>
          </div>
        ) : (
          <div className="streams-table">
            <table>
              <thead>
                <tr>
                  <th>Title</th>
                  <th>Status</th>
                  <th>Viewers</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {myStreams.map((stream) => (
                  <tr key={stream.id}>
                    <td>
                      <div className="stream-title">
                        <strong>{stream.title}</strong>
                        {stream.description && (
                          <p className="stream-desc">{stream.description}</p>
                        )}
                      </div>
                    </td>
                    <td>
                      <span className={`status-badge ${stream.status}`}>
                        {stream.status.toUpperCase()}
                      </span>
                    </td>
                    <td>{stream.viewer_count}</td>
                    <td>{new Date(stream.created_at).toLocaleDateString()}</td>
                    <td>
                      <div className="action-buttons">
                        <Link
                          to={`/stream/${stream.id}`}
                          className="stream-btn"
                        >
                          {stream.status === 'live' ? 'Manage Stream' : 'Start Streaming'}
                        </Link>
                        <Link
                          to={`/stream/${stream.id}`}
                          className="view-btn"
                        >
                          View
                        </Link>
                        <button
                          onClick={() => handleDeleteStream(stream.id)}
                          className="delete-btn"
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default Dashboard;
